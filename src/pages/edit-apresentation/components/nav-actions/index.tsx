import { INavElement, navElements } from '@/shared/constants/my-figma';
import { Avatar, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, Image } from '@nextui-org/react';
import { motion } from 'framer-motion';
import { ArrowLeft, HelpCircle, LogOut, User } from 'lucide-react';
import { useHelpModal } from '../../hooks/help/help-modal.hook';
import { useNavActions } from '../../hooks/navbar/nav-actions';
import { KeyboardShortcutsModal } from '../help/keyboard-shortcuts-modal';
import { AutoTooltip } from '../properties-panel/shared/common/ui/tooltip';
import { NavItem } from './nav-item';

interface NavLogoProps {
	onClick: () => void;
}

const NavLogo: React.FC<NavLogoProps> = ({ onClick }) => (
	<Image
		src="/assets/svgs/reduced-logo.svg"
		className="cursor-pointer rounded-none transition-transform duration-300 hover:scale-105"
		alt="Stream Hub Logo"
		width={32}
		onClick={onClick}
	/>
);

const motionConfig = {
	left: {
		initial: { opacity: 0, x: -100 },
		animate: { opacity: 1, x: 0 },
		transition: { duration: 0.5, delay: 0.2 },
	},
	center: {
		initial: { opacity: 0, y: -100 },
		animate: { opacity: 1, y: 0 },
		transition: { duration: 0.5 },
	},
	right: {
		initial: { opacity: 0, x: 100 },
		animate: { opacity: 1, x: 0 },
		transition: { duration: 0.5 },
	},
};

const mockUser = {
	name: 'João Silva',
	email: '<EMAIL>',
	avatar: 'https://i.pravatar.cc/150',
};

export const NavActions: React.FC = () => {
	const { handleNavigateToPresentation, handleNavigateToHome } = useNavActions();
	const { isOpen, openModal, closeModal } = useHelpModal();

	return (
		<>
			<KeyboardShortcutsModal isOpen={isOpen} onClose={closeModal} />
			<nav className="hidden select-none items-center justify-between gap-4 rounded-md px-5 py-2 shadow-xl lg:flex">
				<motion.div {...motionConfig.left} className="flex items-center gap-5">
					<AutoTooltip text="Voltar para apresentações" preferredPlacement="top">
						<motion.button
							className="flex cursor-pointer items-center gap-2 rounded-lg border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 p-1 px-4 shadow-md transition-transform duration-300 hover:scale-105 hover:shadow-lg"
							onClick={handleNavigateToPresentation}
							whileTap={{ scale: 0.95 }}
						>
							<ArrowLeft className="text-white" />
						</motion.button>
					</AutoTooltip>
					<AutoTooltip text="Stream Hub - Tela inicial" preferredPlacement="top">
						<NavLogo onClick={handleNavigateToHome} />
					</AutoTooltip>
				</motion.div>
				<motion.ul {...motionConfig.center} className="flex gap-2">
					{navElements.map((element: INavElement) => (
						<NavItem key={element.name} item={element} />
					))}
				</motion.ul>
				<motion.div {...motionConfig.right} className="flex items-center gap-3">
					{/* Botão de Ajuda */}
					<AutoTooltip text="Atalhos de teclado (F1 ou ?)" preferredPlacement="top">
						<motion.button
							className="flex cursor-pointer items-center gap-2 rounded-lg border border-primary/30 bg-primary/10 p-2 shadow-md transition-all duration-300 hover:scale-105 hover:bg-primary/20 hover:shadow-lg"
							onClick={openModal}
							whileTap={{ scale: 0.95 }}
							aria-label="Abrir ajuda de atalhos"
						>
							<HelpCircle className="h-5 w-5 text-primary" />
						</motion.button>
					</AutoTooltip>

					<Dropdown placement="bottom-end">
						<DropdownTrigger>
							<button
								type="button"
								className="flex items-center gap-2 rounded-md px-1 py-1 transition hover:bg-gray-800/50 focus:outline-none"
								aria-label="Abrir menu do usuário"
							>
								<Avatar
									size="sm"
									src={mockUser.avatar}
									className="ring-2 ring-green-400 ring-offset-1 ring-offset-[#121214] transition-transform hover:scale-105"
								/>
							</button>
						</DropdownTrigger>
						<DropdownMenu
							aria-label="Ações do perfil"
							className="border border-gray-800 bg-[#1c1c20]"
							// onAction={(key) => key === 'logout' && handleLogout()}
						>
							<DropdownItem
								key="profile"
								startContent={<User size={16} className="text-green-400" />}
								className="text-white hover:bg-green-400/10"
							>
								Meu Perfil
							</DropdownItem>
							<DropdownItem key="logout" startContent={<LogOut size={16} />} className="text-red-400 hover:bg-red-400/10">
								Sair
							</DropdownItem>
						</DropdownMenu>
					</Dropdown>
				</motion.div>
			</nav>
		</>
	);
};
