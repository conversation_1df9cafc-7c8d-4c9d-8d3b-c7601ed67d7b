import { presentationInfoAtom } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';
import { IItem } from '@/pages/edit-apresentation/types/item.type';
import { itemsAtom, selectedItemsIdsAtom, updateItemAtom } from '@/shared/states/items/object-item.state';
import { useAtomValue, useSetAtom } from 'jotai';
import { useState } from 'react';
import { ValidationResult } from '../types/properties.types';

export const usePropertiesPanel = () => {
	const items = useAtomValue(itemsAtom);
	const presentation = useAtomValue(presentationInfoAtom);
	const selectedIds = useAtomValue(selectedItemsIdsAtom);
	const updateItem = useSetAtom(updateItemAtom);
	const [widthError, setWidthError] = useState('');
	const [heightError, setHeightError] = useState('');

	const maxWidth = presentation?.width ?? 0;
	const maxHeight = presentation?.height ?? 0;
	const selectedItems = items.filter((item) => selectedIds.includes(item.tempId));

	const validateDimension = (dimension: 'width' | 'height', value: number): ValidationResult => {
		const max = dimension === 'width' ? maxWidth : maxHeight;
		if (value > max) {
			return { isValid: false, error: `max: ${max}px` };
		}
		return { isValid: true, error: '' };
	};

	const handleItemChange = (item: IItem, field: keyof IItem, value: any) => {
		updateItem({ ...item, [field]: value });
	};

	const handleSizeChange = (item: IItem, dimension: keyof IItem['size'], value: number) => {
		const validation = validateDimension(dimension as 'width' | 'height', value);

		if (dimension === 'width') {
			setWidthError(validation.error);
		} else {
			setHeightError(validation.error);
		}

		if (validation.isValid) {
			updateItem({
				...item,
				size: { ...item.size, [dimension]: value },
			});
		}
	};

	return {
		selectedItems,
		presentation,
		widthError,
		heightError,
		handleItemChange,
		handleSizeChange,
	};
};
