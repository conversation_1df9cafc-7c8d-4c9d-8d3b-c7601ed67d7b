import { useRef } from 'react';

import { useForm<PERSON>andler } from '@/pages/edit-apresentation/hooks/utils/form-state.hook';

import { DEFAULT_CAROUSEL_OBJECT } from '@/pages/edit-apresentation/data/base-format-elements/caroulsel';
import { ICarouselObject } from './corousel.types';

const debounce = <T extends (...args: any[]) => void>(func: T, wait: number): ((...args: Parameters<T>) => void) => {
	let timeout: ReturnType<typeof setTimeout>;
	return (...args: Parameters<T>) => {
		const later = () => {
			clearTimeout(timeout);
			func(...args);
		};
		clearTimeout(timeout);
		timeout = setTimeout(later, wait);
	};
};

export function useCarouselForm(content: ICarouselObject | undefined, onChange: (content: ICarouselObject) => void) {
	const {
		formData: carouselState,
		setFormData: setCarouselState,
		updateField: updateCarouselField,
	} = useFormHandler<ICarouselObject>(DEFAULT_CAROUSEL_OBJECT, content, onChange);

	const debouncedUpdateField = useRef(debounce((...args: Parameters<typeof updateCarouselField>) => updateCarouselField(...args), 300)).current;

	return {
		carouselState,
		setCarouselState,
		updateCarouselFieldDebounced: debouncedUpdateField,
		updateCarouselField,
	};
}
