import { SelectItemProperties } from '../../../../shared/common';
import { IShapeSquareStyle, TBackgroundStyleType, TGradientShape } from '../../square-editor.types';
import { BACKGROUND_STYLES } from '../../square.data';
import { LinearGradientEditor } from './linear';
import { OpaqueBackgroundEditor } from './opaque';
import { RadialGradientEditor } from './radial';

interface ISquareBackgroundOptionsProps {
	formData: IShapeSquareStyle;
	setField: (field: keyof IShapeSquareStyle, value: any) => void;
}

function getLinearGradientProps(formData: IShapeSquareStyle) {
	return {
		gradientOptions: {
			...formData.gradientOptions,
			angle: formData.gradientOptions?.angle ?? 0,
			stops: [
				formData.gradientOptions?.stops?.[0] ?? { color: formData.backgroundColor ?? '#ffffff', offset: 0 },
				formData.gradientOptions?.stops?.[1] ?? { color: '#000000', offset: 1 },
			],
		},
	};
}

function getRadialGradientProps(formData: IShapeSquareStyle) {
	return {
		gradientOptions: {
			...formData.gradientOptions,
			position: formData.gradientOptions?.position ?? { x: 50, y: 50 },
			shape: (formData.gradientOptions?.shape as TGradientShape) ?? 'circle',
			stops: [
				formData.gradientOptions?.stops?.[0] ?? { color: formData.backgroundColor ?? '#ffffff', offset: 0 },
				formData.gradientOptions?.stops?.[1] ?? { color: '#000000', offset: 1 },
			],
		},
	};
}

export function SquareBackgroundOptions({ formData, setField }: Readonly<ISquareBackgroundOptionsProps>) {
	const handleChangeBackgroundStyle = (newStyle: TBackgroundStyleType) => {
		if (newStyle !== formData.backgroundStyle) {
			if (newStyle === 'opaque' && !formData.backgroundColor) {
				const fallbackColor = formData.gradientOptions?.stops?.[0]?.color ?? '#ffffff';
				setField('backgroundColor', fallbackColor);
			}
			setField('backgroundStyle', newStyle);
		}
	};

	const handleChangeGradientOptions = (newOptions: any) => {
		setField('gradientOptions', newOptions);
	};

	const renderEditor = () => {
		switch (formData.backgroundStyle) {
			case 'opaque':
				return <OpaqueBackgroundEditor color={formData.backgroundColor} onChange={(color) => setField('backgroundColor', color)} />;
			case 'linearGradient':
				return <LinearGradientEditor {...getLinearGradientProps(formData)} onChange={handleChangeGradientOptions} />;
			case 'radialGradient':
				return <RadialGradientEditor {...getRadialGradientProps(formData)} onChange={handleChangeGradientOptions} />;
			default:
				return null;
		}
	};

	return (
		<div className="w-full">
			<div className="flex flex-col gap-2">
				<div className="mt-2">
					<SelectItemProperties
						value={formData.backgroundStyle}
						onValueChange={(value) => handleChangeBackgroundStyle(value as TBackgroundStyleType)}
						items={BACKGROUND_STYLES}
						labelText="Estilo:"
					/>
				</div>
				{renderEditor()}
			</div>
		</div>
	);
}
