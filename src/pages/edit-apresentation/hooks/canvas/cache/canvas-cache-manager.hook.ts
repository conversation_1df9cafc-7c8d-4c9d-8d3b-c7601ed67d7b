import Konva from 'konva';
import { useCallback, useRef } from 'react';

interface CanvasCacheManagerOptions {
	stageRef?: React.RefObject<Konva.Stage>;
	enableDebugLogs?: boolean;
}

export const useCanvasCacheManager = ({ stageRef, enableDebugLogs = false }: CanvasCacheManagerOptions = {}) => {
	const lastCleanupTime = useRef<number>(0);
	const cleanupThrottle = 16; // ~60fps

	const log = useCallback(
		(message: string) => {
			if (enableDebugLogs) {
				console.log(`[CanvasCacheManager] ${message}`);
			}
		},
		[enableDebugLogs],
	);

	const lightCleanup = useCallback(
		(stage?: Konva.Stage) => {
			const now = Date.now();
			if (now - lastCleanupTime.current < cleanupThrottle) {
				return;
			}
			lastCleanupTime.current = now;

			const targetStage = stage || stageRef?.current;
			if (!targetStage) return;

			log('Executando limpeza leve do cache');
			targetStage.clearCache();
		},
		[stageRef, log, cleanupThrottle],
	);

	const fullCleanup = useCallback(
		(stage?: Konva.Stage) => {
			const targetStage = stage || stageRef?.current;
			if (!targetStage) return;

			log('Executando limpeza completa do cache');

			targetStage.getLayers().forEach((layer: any) => {
				layer.clearCache();
				layer.draw();
			});

			targetStage.clearCache();
			targetStage.draw();
		},
		[stageRef, log],
	);

	const delayedCleanup = useCallback(
		(stage?: Konva.Stage, delay: number = 0) => {
			const targetStage = stage || stageRef?.current;
			if (!targetStage) return;

			if (delay === 0) {
				requestAnimationFrame(() => fullCleanup(targetStage));
			} else {
				setTimeout(() => fullCleanup(targetStage), delay);
			}
		},
		[fullCleanup, stageRef],
	);

	const onDragStartCleanup = useCallback(
		(stage?: Konva.Stage) => {
			const targetStage = stage || stageRef?.current;
			if (!targetStage) return;

			log('Limpeza para início de drag');

			lightCleanup(targetStage);
			targetStage.getLayers().forEach((layer: any) => {
				layer.getChildren().forEach((node: any) => {
					if (node.perfectDrawEnabled) {
						node.perfectDrawEnabled(false);
					}
				});
			});
		},
		[lightCleanup, stageRef, log],
	);

	const onDragMoveCleanup = useCallback(
		(stage?: Konva.Stage) => {
			const targetStage = stage || stageRef?.current;
			if (!targetStage) return;
			lightCleanup(targetStage);
		},
		[lightCleanup, stageRef],
	);

	const onDragEndCleanup = useCallback(
		(stage?: Konva.Stage) => {
			const targetStage = stage || stageRef?.current;
			if (!targetStage) return;

			log('Limpeza para fim de drag');

			targetStage.getLayers().forEach((layer: any) => {
				layer.getChildren().forEach((node: any) => {
					if (node.perfectDrawEnabled !== undefined) {
						const isSelected = node.attrs?.stroke === 'green' && node.attrs?.strokeWidth > 0;
						node.perfectDrawEnabled(!isSelected);
					}
				});
			});

			fullCleanup(targetStage);
			delayedCleanup(targetStage, 16);
		},
		[fullCleanup, delayedCleanup, stageRef, log],
	);

	const forceFullCleanup = useCallback(
		(stage?: Konva.Stage) => {
			const targetStage = stage || stageRef?.current;
			if (!targetStage) return;

			log('Forçando limpeza completa do canvas');

			fullCleanup(targetStage);
			delayedCleanup(targetStage, 0);
			delayedCleanup(targetStage, 16);
			delayedCleanup(targetStage, 32);
		},
		[fullCleanup, delayedCleanup, stageRef, log],
	);

	return {
		lightCleanup,
		fullCleanup,
		delayedCleanup,
		onDragStartCleanup,
		onDragMoveCleanup,
		onDragEndCleanup,
		forceFullCleanup,
	};
};
