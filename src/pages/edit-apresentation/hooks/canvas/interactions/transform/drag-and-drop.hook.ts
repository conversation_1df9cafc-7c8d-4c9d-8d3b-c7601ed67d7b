import { IItem } from '@/pages/edit-apresentation/types/item.type';
import Konva from 'konva';
import { useCallback } from 'react';
import { useCanvasCacheManager } from '../../cache/canvas-cache-manager.hook';

interface UseDragAndDropProps {
	items: IItem[];
	setItems: (items: IItem[]) => void;
	selectedsIds: string[];
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	clearSnapLines: () => void;
	checkSnapLines: (currentItem: IItem) => void;
}

const clamp = (value: number, min: number, max: number): number => Math.min(Math.max(value, min), max);

export function useDragAndDrop({ items, setItems, selectedsIds, scale, canvasWidth, canvasHeight, clearSnapLines, checkSnapLines }: UseDragAndDropProps) {
	const cacheManager = useCanvasCacheManager({ enableDebugLogs: false });

	const handleDragStart = useCallback(
		({ id }: { id: string }) => {
			if (!selectedsIds.includes(id)) return;
			setItems(items.map((item) => (selectedsIds.includes(item.tempId) ? { ...item, isDragging: true } : item)));
		},
		[selectedsIds, setItems, items],
	);

	const handleDragEnd = useCallback(
		({ id, event }: { id: string; event: Konva.KonvaEventObject<MouseEvent> }) => {
			const { x, y } = event.target.position();
			const realX = x / scale;
			const realY = y / scale;
			const draggedItem = items.find((item) => item.tempId === id);
			if (!draggedItem) return;

			const deltaX = realX - draggedItem.position.x;
			const deltaY = realY - draggedItem.position.y;

			const updatePosition = (item: IItem) => {
				const newX = clamp(item.position.x + deltaX, 0, canvasWidth - item.size.width);
				const newY = clamp(item.position.y + deltaY, 0, canvasHeight - item.size.height);
				return { ...item, position: { x: newX, y: newY }, isDragging: false };
			};

			setItems(items.map((item) => (selectedsIds.includes(item.tempId) ? updatePosition(item) : item)));
			clearSnapLines();

			// Usar o gerenciador de cache para limpeza completa
			const stage = event.target.getStage();
			if (stage) {
				cacheManager.onDragEndCleanup(stage);
			}
		},
		[items, setItems, scale, canvasWidth, canvasHeight, selectedsIds, clearSnapLines, cacheManager],
	);

	const handleDragMove = useCallback(
		({ id, event }: { id: string; event: Konva.KonvaEventObject<MouseEvent> }) => {
			const { x, y } = event.target.position();
			const currentItem = items.find((item) => item.tempId === id);
			if (!currentItem) return;

			const draggedItem = {
				...currentItem,
				position: { x: x / scale, y: y / scale },
			};

			checkSnapLines(draggedItem);
			const stage = event.target.getStage();
			if (stage) {
				cacheManager.onDragMoveCleanup(stage);
			}
		},
		[items, scale, checkSnapLines, cacheManager],
	);

	return { handleDragStart, handleDragEnd, handleDragMove };
}
