import Konva from 'konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { Vector2d } from 'konva/lib/types';

export interface IShapeConfiguration {
	id: string;
	x: number;
	y: number;
	width: number;
	height: number;
	isEditing?: boolean;
	isSelected: boolean;
	onClick: (e: KonvaEventObject<MouseEvent>) => void;
	onDragStart: () => void;
	onDragEnd: (e: KonvaEventObject<MouseEvent>) => void;
	onTransformEnd: (e: KonvaEventObject<MouseEvent>) => void;
	canvasWidth: number;
	canvasHeight: number;
	onMouseMove: (e: KonvaEventObject<MouseEvent>) => void;
	onMouseLeave: () => void;
	currentlyHoveredItem: { name: string; width: number; height: number; tempId: string } | null;
	onDragMove: (args: { id: string; event: KonvaEventObject<MouseEvent> }) => void;
	showOnlyOutline?: boolean;
	opacity?: number;
	stroke?: string;
	strokeWidth?: number;
	fill?: string;
	padding?: number;
}

export interface IShapeRenderOptions {
	isSelected: boolean;
	isEditing?: boolean;
	onClick: (e: KonvaEventObject<MouseEvent>) => void;
	onDragEnd: (e: KonvaEventObject<MouseEvent>) => void;
	onTransformEnd: (e: KonvaEventObject<MouseEvent>) => void;
	onDragStart: () => void;
	onDragMove: (args: { id: string; event: KonvaEventObject<MouseEvent> }) => void;
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	onMouseMove: (e: KonvaEventObject<MouseEvent>) => void;
	onMouseLeave: () => void;
	currentlyHoveredItem: { name: string; width: number; height: number; tempId: string } | null;
	showOnlyOutline?: boolean;
	opacity?: number;
	stroke?: string;
	strokeWidth?: number;
	fill?: string;
	padding?: number;
}

export interface IShapesLayerProps {
	onSelectShape: (args: { idElement: string; event: KonvaEventObject<MouseEvent> }) => void;
	onDragEnd: (args: { id: string; event: KonvaEventObject<MouseEvent> }) => void;
	onTransformEnd: (e: KonvaEventObject<MouseEvent>) => void;
	onDragStart: (args: { id: string }) => void;
	onDragMove: ({ id, event }: { id: string; event: Konva.KonvaEventObject<MouseEvent> }) => void;
	onTransformStart: (e: KonvaEventObject<MouseEvent>, id: string) => void;
	transformerRef: React.RefObject<any>;
	scale: number;
	hoverPosition: Vector2d | null;
	canvasWidth: number;
	canvasHeight: number;
	onShapeMouseEnter: (args: { name: string; width: number; height: number; tempId: string }, pos: Vector2d) => void;
	onShapeMouseLeave: () => void;
	selectionBox: { x: number; y: number; width: number; height: number } | null;
	clearHoveredShape: () => void;
	handleTransform: (e: KonvaEventObject<Event>) => void;
	currentlyHoveredItem: { name: string; width: number; height: number; tempId: string } | null;
	padding?: number;
	editableAreaWidth?: number;
	editableAreaHeight?: number;
}
